{"name": "@paypal/checkout-server-sdk", "version": "1.0.3", "description": "NodeJS SDK for PayPal Checkout APIs", "keywords": [], "homepage": "https://github.com/paypal/Checkout-NodeJS-SDK#readme", "author": "<EMAIL> (https://developer.paypal.com/)", "main": "index", "directories": {"lib": "lib"}, "repository": {"type": "git", "url": "https://github.com/paypal/Checkout-NodeJS-SDK.git"}, "engines": {"node": ">=8"}, "dependencies": {"@paypal/paypalhttp": "^1.0.1"}, "devDependencies": {"btoa": "^1.2.1", "chai": "^4.1.2", "dirty-chai": "^2.0.1", "eslint": "^2.7.0", "eslint-config-braintree": "^1.0.0", "mocha": "^5.2.0", "prompt": "^1.0.0", "nock": "^9.6.1", "sinon": "^2.3.2"}, "license": "SEE LICENSE IN https://github.com/paypal/Checkout-NodeJS-SDK/blob/master/LICENSE", "scripts": {"test:integration": "mocha spec --recursive", "test": "npm run test:integration", "test:orders": "mocha spec/orders --recursive --timeout 60000"}, "bugs": {"url": "https://github.com/paypal/Checkout-NodeJS-SDK/issues"}}