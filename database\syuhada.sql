-- ===========================================
-- ALTER TABLE: `faq`
-- ===========================================

-- Drop unused column
ALTER TABLE `mydb`.`faq`
DROP COLUMN `submitted_by_user_id`;

-- Add category for organizing questions
ALTER TABLE `mydb`.`faq`
ADD COLUMN `category` VARCHAR(50) NULL AFTER `question`;

-- Modify ENUM status to include states
ALTER TABLE `mydb`.`faq`
CHANGE COLUMN `status` `status` ENUM('pending', 'answered', 'archived') NOT NULL DEFAULT 'pending';

-- Add flag to control publication state
ALTER TABLE `mydb`.`faq`
ADD COLUMN `is_published` ENUM('yes', 'no') NOT NULL DEFAULT 'no' AFTER `status`;

-- Add published timestamp
ALTER TABLE `mydb`.`faq`
ADD COLUMN `published_at` TIMESTAMP NULL DEFAULT NULL AFTER `is_published`;

-- Make user ID nullable for anonymous questions
ALTER TABLE `mydb`.`faq`
CHANGE COLUMN `users_user_id` `users_user_id` INT(11) NULL DEFAULT NULL;

-- ===========================================
-- ALTER TABLE: `matches`
-- ===========================================

-- Remove player reference (no longer needed)
ALTER TABLE `mydb`.`matches`
DROP FOREIGN KEY `fk_matches_players1`;

ALTER TABLE `mydb`.`matches`
DROP INDEX `fk_matches_players1_idx`;

ALTER TABLE `mydb`.`matches`
DROP COLUMN `player_id`;

-- Add column for extra match notes
ALTER TABLE `mydb`.`matches`
ADD COLUMN `match_notes` TEXT NULL AFTER `result`;

-- Update column data types
ALTER TABLE `mydb`.`matches`
CHANGE COLUMN `season` `season` VARCHAR(45) NULL DEFAULT NULL;

ALTER TABLE `mydb`.`matches`
CHANGE COLUMN `match_date` `match_date` DATETIME NULL DEFAULT NULL;

-- Refine status ENUM values
ALTER TABLE `mydb`.`matches`
CHANGE COLUMN `status` `status` ENUM('scheduled', 'live', 'completed', 'postponed', 'cancelled') NOT NULL DEFAULT 'scheduled';

-- Refine result ENUM values
ALTER TABLE `mydb`.`matches`
CHANGE COLUMN `result` `result` ENUM('win', 'loss', 'draw') NULL DEFAULT NULL;

-- Allow match to be created without a schedule
ALTER TABLE `mydb`.`matches`
MODIFY COLUMN `schedule_id` INT(11) NULL DEFAULT NULL;

-- Add indexes for performance optimization
CREATE INDEX `idx_season` ON `mydb`.`matches` (`season`);
CREATE INDEX `idx_match_date` ON `mydb`.`matches` (`match_date`);

-- ===========================================
-- INSERT INTO `news`
-- ===========================================
INSERT INTO `mydb`.`news` 
(`author_id`, `title`, `content`, `summary`, `featured_image`, `category`, `status`, `published_at`, `created_at`, `updated_at`, `users_user_id`) 
VALUES
(1, 'New Season Kicks Off with a Bang!',
'The highly anticipated new football season has officially begun, and it\'s already delivering thrilling matches and unexpected results. Fans are excited to see their favorite teams back in action.',
'Exciting start to the new football season with thrilling matches.',
'/images/news/news_season.png', 'Season Updates', 'published', '2025-07-25 06:13:58', '2025-07-24 10:51:44', '2025-07-25 06:13:58', 1),
(1, 'Player Spotlight: Rising Star Ryan Koh',
'Get to know Ryan Koh, the young talent who has been making waves with his exceptional performance on the field. His dedication and skill are truly inspiring.',
'An in-depth look at Ryan Koh, a rising football star.',
'/images/news/news5.png', 'Player Profiles', 'published', '2025-07-25 06:16:53', '2025-07-24 10:51:44', '2025-07-25 06:16:53', 1),
(1, 'Club Announces New Community Initiative',
'Our club is proud to launch a new community initiative aimed at promoting youth sports and healthy living in local neighborhoods. We believe in giving back to our community.',
'Football club launches new initiative for youth sports.',
'/images/news/news3.png', 'Community', 'published', '2025-07-25 06:14:36', '2025-07-24 10:51:44', '2025-07-25 06:14:36', 1),
(1, 'Injury Update: Star Player Out for Weeks',
'Unfortunately, our key player, Alex Tan, has sustained an injury during the last match and will be out of action for several weeks. We wish them a speedy recovery.',
'Important update on a star player\'s injury.',
'/images/news/news6.png', 'Team News', 'published', '2025-07-25 06:16:28', '2025-07-24 10:51:44', '2025-07-25 06:16:28', 1),
(1, 'Raffles Rangers Crowned Champions!',
'At the prestigious Singapore Premier League (SPL) Awards Night, Raffles Rangers Football Club was proudly recognized as the AIA SPL Team of the Year. This esteemed award celebrates their outstanding performance and collective excellence throughout the season.',
'Raffles Rangers honored at the Singapore Premier League Awards Night.',
'/images/news/news2.png', 'Awards', 'published', '2025-07-25 06:18:08', '2025-07-24 10:56:29', '2025-07-25 06:18:08', 1);

-- ===========================================
-- INSERT INTO `faq`
-- ===========================================
INSERT INTO `mydb`.`faq` 
(`question`, `category`, `answer`, `status`, `is_published`, `display_order`, `users_user_id`) 
VALUES
('How do I purchase match tickets?', 'Tickets', 
'You can purchase match tickets directly from our official website by navigating to the "Tickets" section and selecting your desired match.', 
'answered', 'yes', 1, 1),

('What are the benefits of a membership?', 'Membership', 
'Our membership tiers offer various benefits including discounted tickets, exclusive merchandise access, and priority event invitations. Please refer to the "Membership" page for full details.', 
'answered', 'yes', 3, 1),

('I forgot my password. How can I reset it?', 'Technical', 
'To reset your password, click on "Forgot Password" on the login page and follow the instructions sent to your registered email address.', 
'answered', 'yes', 1, 1),

('Can I exchange my gear if it does not fit?', 'Store', 
'Yes, we offer exchanges for gear within 30 days of purchase, provided the item is unworn and in its original packaging. Please see our returns policy for more information.', 
'answered', 'yes', 1, 1);

-- ===========================================
-- INSERT INTO `matches`
-- ===========================================
INSERT INTO `mydb`.`matches` 
(`home_team`, `away_team`, `home_score`, `away_score`, `season`, `competition`, `match_date`, `venue`, `status`, `result`, `match_notes`, `schedule_id`) 
VALUES
('Raffles Rangers', 'United Titans', 2, 1, '2025/2026', 'Premier League', '2025-08-10 19:30:00', 'National Stadium', 'completed', 'win', 'A thrilling comeback victory for Raffles Rangers!', NULL),
('Raffles Rangers', 'Northern Lights', 0, 0, '2025/2026', 'Cup Championship', '2025-08-15 21:00:00', 'City Arena', 'scheduled', NULL, 'Anticipated intense rivalry match.', NULL),
('Raffles Rangers', 'Desert Wolves', 3, 3, '2025/2026', 'Friendly Match', '2025-07-25 17:00:00', 'Training Ground', 'live', 'draw', 'Currently in the second half.', NULL),
('Raffles Rangers', 'River Rovers', 1, 0, '2025/2026', 'Premier League', '2025-08-01 20:45:00', 'Valley Stadium', 'completed', 'win', 'Raffles Rangers secured a narrow victory.', NULL);