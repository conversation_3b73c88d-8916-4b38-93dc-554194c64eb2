/*footer*/
html, body {
    height: 100%;
  }

  body {
    display: flex;
    flex-direction: column;
  }

  main {
    flex: 1;
  }

/* -- NEWS EJS -- */
/* News section */
.card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  border-radius: 12px;
  transition: box-shadow 0.2s;
}
.card:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.10);
}

.card-title {
  font-weight: 600;
  font-size: 1.1rem;
}

/* Featured News */
.featured-news-img {
  width: 100%;
  height: 260px;
  object-fit: cover;
  border-radius: 12px 0 0 12px;
}

/* Responsive grid for news cards */
@media (max-width: 991px) {
  .col-md-3 {
    flex: 0 0 50%;
    max-width: 50%;
  }
}
@media (max-width: 575px) {
  .col-md-3 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.status-badge.scheduled {
  background-color: #3498db;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  position: relative;
  margin-right: 4px;
}
.status-badge.scheduled::before {
  content: "\23F0 "; /* clock emoji */
  margin-right: 2px;
}
.countdown-timer {
  font-size: 11px;
  color: #666;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
  display: inline-block;
  margin-left: 8px;
}
.card .display-6#scheduled-stat {
  color: #3498db;
}