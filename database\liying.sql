-- ======================================
-- SECTION 1: Unified Payment Migration
-- ======================================
USE mydb;

-- Insert payment methods
INSERT IGNORE INTO payment_methods (payment_method_id, method_name, gateway_url, is_active) VALUES
(1, 'Stripe', 'https://stripe.com', 1),
(2, 'PayPal', 'https://paypal.com', 1);

-- Update payments table to support unified payment type
ALTER TABLE payments 
MODIFY COLUMN payment_type ENUM('gear', 'ticket', 'membership', 'unified') DEFAULT 'unified';

-- Clean up and fix purchase_id column in ticket_purchases
UPDATE ticket_purchases SET purchase_id = NULL WHERE purchase_id <= 0;
ALTER TABLE ticket_purchases 
MODIFY COLUMN purchase_id INT(11) NOT NULL AUTO_INCREMENT;

-- ======================================
-- SECTION 2: Refund System Updates
-- ======================================
-- Enhanced unified refund_requests table for both gear and tickets
CREATE TABLE IF NOT EXISTS refund_requests (
    refund_id INT AUTO_INCREMENT PRIMARY KEY,
    
    -- Reference fields - use based on refund_type
    order_id INT NULL,              -- For gear refunds
    ticket_purchase_id INT NULL,    -- For ticket refunds
    user_id INT NOT NULL,
    payment_id INT NULL,
    
    -- Refund type and details
    refund_type ENUM('gear', 'ticket') NOT NULL,
    reason VARCHAR(500) NOT NULL,
    detailed_reason TEXT NULL,
    status ENUM('pending', 'approved', 'rejected', 'processed') DEFAULT 'pending',

    -- Financial details
    original_order_amount DECIMAL(10,2) NOT NULL,
    refund_amount DECIMAL(10,2) NOT NULL,
    original_payment_method VARCHAR(100) NULL,
    original_transaction_reference VARCHAR(255) NULL,
    original_payment_date TIMESTAMP NULL,

    -- Items being refunded (JSON for flexibility)
    refunded_items JSON NULL,

    -- Admin management
    admin_notes TEXT NULL,
    admin_decision_reason TEXT NULL,
    refund_transaction_reference VARCHAR(255) NULL,
    refund_method VARCHAR(100) NULL,

    -- Timestamps
    requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_at TIMESTAMP NULL,
    processed_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,

    -- Staff tracking
    processed_by INT NULL,
    refunded_by INT NULL,

    -- Notification tracking
    customer_notified BOOLEAN DEFAULT FALSE,
    refund_confirmation_sent BOOLEAN DEFAULT FALSE,

    -- Indexes for performance
    INDEX idx_refund_status (status),
    INDEX idx_refund_type (refund_type),
    INDEX idx_refund_date (requested_at),
    INDEX idx_user_refunds (user_id, requested_at),
    INDEX idx_order_refunds (order_id),
    INDEX idx_ticket_refunds (ticket_purchase_id),
    
    -- Constraints to ensure proper referencing
    CONSTRAINT chk_refund_reference CHECK (
        (refund_type = 'gear' AND order_id IS NOT NULL AND ticket_purchase_id IS NULL) OR
        (refund_type = 'ticket' AND ticket_purchase_id IS NOT NULL AND order_id IS NULL)
    )
);

-- Foreign key constraints
ALTER TABLE refund_requests 
ADD CONSTRAINT fk_refund_order 
FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE;

ALTER TABLE refund_requests 
ADD CONSTRAINT fk_refund_ticket
FOREIGN KEY (ticket_purchase_id) REFERENCES ticket_purchases(purchase_id) ON DELETE CASCADE;

ALTER TABLE refund_requests 
ADD CONSTRAINT fk_refund_user 
FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE;

ALTER TABLE refund_requests 
ADD CONSTRAINT fk_refund_payment 
FOREIGN KEY (payment_id) REFERENCES payments(payment_id) ON DELETE SET NULL;

ALTER TABLE refund_requests 
ADD CONSTRAINT fk_refund_processed_by 
FOREIGN KEY (processed_by) REFERENCES users(user_id) ON DELETE SET NULL;

ALTER TABLE refund_requests 
ADD CONSTRAINT fk_refund_refunded_by 
FOREIGN KEY (refunded_by) REFERENCES users(user_id) ON DELETE SET NULL;

-- ======================================
-- SECTION 3: Reservations Tables
-- ======================================
CREATE TABLE IF NOT EXISTS `mydb`.`reservations` (
  `reservation_id` INT NOT NULL AUTO_INCREMENT,
  `user_id` INT NOT NULL,
  `reservation_date` DATE NOT NULL,
  `reservation_time` TIME NOT NULL,
  `party_size` INT NOT NULL DEFAULT 1,
  `notes` TEXT NULL,
  `status` ENUM('pending', 'confirmed', 'cancelled', 'completed') DEFAULT 'pending',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`reservation_id`),
  INDEX `fk_reservations_users_idx` (`user_id` ASC),
  CONSTRAINT `fk_reservations_users`
    FOREIGN KEY (`user_id`)
    REFERENCES `mydb`.`users` (`user_id`)
    ON DELETE CASCADE
    ON UPDATE CASCADE
) ENGINE = InnoDB;

CREATE TABLE IF NOT EXISTS `mydb`.`reservation_time_slots` (
  `slot_id` INT NOT NULL AUTO_INCREMENT,
  `time_slot` TIME NOT NULL,
  `max_capacity` INT NOT NULL DEFAULT 4,
  `is_active` BOOLEAN DEFAULT TRUE,
  PRIMARY KEY (`slot_id`),
  UNIQUE KEY `unique_time_slot` (`time_slot`)
) ENGINE = InnoDB;

INSERT INTO `mydb`.`reservation_time_slots` (`time_slot`, `max_capacity`) VALUES
('09:00:00', 4),
('10:00:00', 4),
('11:00:00', 4),
('12:00:00', 4),
('13:00:00', 4),
('14:00:00', 4),
('15:00:00', 4),
('16:00:00', 4),
('17:00:00', 4),
('18:00:00', 4),
('19:00:00', 4),
('20:00:00', 4)
ON DUPLICATE KEY UPDATE max_capacity = VALUES(max_capacity);

ALTER TABLE `mydb`.`orders`
ADD COLUMN `is_active` TINYINT(1) NOT NULL DEFAULT 1 AFTER `updated_at`;


INSERT INTO `gear` (`gear_id`, `gear_name`, `gear_image`, `gear_desc`, `gear_quantity`, `price_per_unit`, `category`, `status`, `created_at`, `updated_at`) 
VALUES 
(NULL, 'White Cap', 'whitecap.png', 'White Cap with Raffles Rangers logo embossed', '10', '29.99', 'Accessories', 'available', current_timestamp(), current_timestamp()),
(NULL, 'Black Cap', 'blackcap.png', 'Black Cap with Raffles Rangers logo embossed', '10', '29.99', 'Accessories', 'available', current_timestamp(), current_timestamp()),
(NULL, 'Orange Cap', 'orangecap.png', 'Orange Cap with Raffles Rangers logo embossed', '10', '29.99', 'Accessories', 'available', current_timestamp(), current_timestamp()),
(NULL, 'White Mug', 'whitemug.png', 'White Mug with Raffles Rangers logo embossed', '10', '14.99', 'Gifting', 'available', current_timestamp(), current_timestamp()),
(NULL, 'Black Mug', 'blackmug.png', 'Black Mug with Raffles Rangers logo embossed', '10', '14.99', 'Gifting', 'available', current_timestamp(), current_timestamp()),
(NULL, 'Keychain', 'keychain.png', 'Keychain of Raffles Rangers Logo', '10', '9.99', 'Gifting', 'available', current_timestamp(), current_timestamp()),
(NULL, 'Banner', 'banner.png', 'Banner of Raffles Rangers', '10', '19.99', 'Accessories', 'available', current_timestamp(), current_timestamp()),
(NULL, 'White Socks', 'whitesocks.png', 'White knee-high socks with Raffles Rangers logo embossed', '10', '29.99', 'Accessories', 'available', current_timestamp(), current_timestamp()),
(NULL, 'Black Socks', 'blacksocks.png', 'Black knee-high socks with Raffles Rangers logo embossed', '10', '29.99', 'Accessories', 'available', current_timestamp(), current_timestamp()),
(NULL, 'White Jersey', 'whitejersey.png', 'White Home Jersey with Raffles Rangers logo embossed', '10', '99.99', 'Jersey', 'available', current_timestamp(), current_timestamp()),
(NULL, 'Black Jersey', 'blackjersey.png', 'Black Home Jersey with Raffles Rangers logo embossed', '10', '99.99', 'Jersey', 'available', current_timestamp(), current_timestamp());

-- Migration to add gear sizes functionality
-- Run this SQL script to update your database

-- Migration to add gear sizes functionality
-- Run this SQL script to update your database

-- Create gear_sizes table to store available sizes for each gear item
CREATE TABLE IF NOT EXISTS `gear_sizes` (
  `size_id` INT NOT NULL AUTO_INCREMENT,
  `gear_id` INT NOT NULL,
  `size_name` VARCHAR(20) NOT NULL, -- e.g., 'XS', 'S', 'M', 'L', 'XL', 'XXL', '38', '40', '42'
  `stock_quantity` INT NOT NULL DEFAULT 0,
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`size_id`),
  INDEX `fk_gear_sizes_gear_idx` (`gear_id` ASC),
  UNIQUE KEY `unique_gear_size` (`gear_id`, `size_name`),
  CONSTRAINT `fk_gear_sizes_gear`
    FOREIGN KEY (`gear_id`)
    REFERENCES `gear` (`gear_id`)
    ON DELETE CASCADE
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- Update the gear table to add a has_sizes flag
ALTER TABLE `gear` 
ADD COLUMN `has_sizes` TINYINT(1) DEFAULT 0 AFTER `category`;

-- Create a view for easier gear management with size info
DROP VIEW IF EXISTS `gear_with_sizes`;
CREATE VIEW `gear_with_sizes` AS
SELECT 
    g.*,
    CASE 
        WHEN g.has_sizes = 1 THEN (
            SELECT COALESCE(SUM(gs.stock_quantity), 0) 
            FROM gear_sizes gs 
            WHERE gs.gear_id = g.gear_id
        )
        ELSE g.gear_quantity
    END as calculated_total_stock,
    CASE 
        WHEN g.has_sizes = 1 THEN (
            SELECT GROUP_CONCAT(
                CONCAT(gs.size_name, ' (', gs.stock_quantity, ')') 
                ORDER BY gs.size_name SEPARATOR ', '
            )
            FROM gear_sizes gs 
            WHERE gs.gear_id = g.gear_id
        )
        ELSE 'No sizes'
    END as size_info
FROM gear g;

-- Create a function to get total stock (including sizes)
DROP FUNCTION IF EXISTS GetGearTotalStock;

DELIMITER //
CREATE FUNCTION GetGearTotalStock(gear_id_param INT) 
RETURNS INT 
READS SQL DATA 
DETERMINISTIC
BEGIN
    DECLARE total_stock INT DEFAULT 0;
    DECLARE has_sizes_flag TINYINT(1) DEFAULT 0;
    
    -- Get the has_sizes flag for this gear
    SELECT has_sizes INTO has_sizes_flag FROM gear WHERE gear_id = gear_id_param;
    
    -- Calculate total stock based on whether item has sizes
    IF has_sizes_flag = 1 THEN
        SELECT COALESCE(SUM(stock_quantity), 0) INTO total_stock 
        FROM gear_sizes WHERE gear_id = gear_id_param;
    ELSE
        SELECT gear_quantity INTO total_stock FROM gear WHERE gear_id = gear_id_param;
    END IF;
    
    RETURN total_stock;
END//
DELIMITER ;

-- For Jersey items (assuming they need sizes)
INSERT INTO gear_sizes (gear_id, size_name, stock_quantity)
SELECT gear_id, 'XS', FLOOR(gear_quantity * 0.1)
FROM gear WHERE category = 'Jersey' AND gear_quantity > 0;

INSERT INTO gear_sizes (gear_id, size_name, stock_quantity)
SELECT gear_id, 'S', FLOOR(gear_quantity * 0.2)
FROM gear WHERE category = 'Jersey' AND gear_quantity > 0;

INSERT INTO gear_sizes (gear_id, size_name, stock_quantity)
SELECT gear_id, 'M', FLOOR(gear_quantity * 0.3)
FROM gear WHERE category = 'Jersey' AND gear_quantity > 0;

INSERT INTO gear_sizes (gear_id, size_name, stock_quantity)
SELECT gear_id, 'L', FLOOR(gear_quantity * 0.25)
FROM gear WHERE category = 'Jersey' AND gear_quantity > 0;

INSERT INTO gear_sizes (gear_id, size_name, stock_quantity)
SELECT gear_id, 'XL', FLOOR(gear_quantity * 0.15)
FROM gear WHERE category = 'Jersey' AND gear_quantity > 0;

-- For Socks items (assuming they need sizes)
INSERT INTO gear_sizes (gear_id, size_name, stock_quantity)
SELECT gear_id, 'S', FLOOR(gear_quantity * 0.25)
FROM gear WHERE (category = 'Accessories' AND gear_name LIKE '%Socks%') AND gear_quantity > 0;

INSERT INTO gear_sizes (gear_id, size_name, stock_quantity)
SELECT gear_id, 'M', FLOOR(gear_quantity * 0.35)
FROM gear WHERE (category = 'Accessories' AND gear_name LIKE '%Socks%') AND gear_quantity > 0;

INSERT INTO gear_sizes (gear_id, size_name, stock_quantity)
SELECT gear_id, 'L', FLOOR(gear_quantity * 0.25)
FROM gear WHERE (category = 'Accessories' AND gear_name LIKE '%Socks%') AND gear_quantity > 0;

INSERT INTO gear_sizes (gear_id, size_name, stock_quantity)
SELECT gear_id, 'XL', FLOOR(gear_quantity * 0.15)
FROM gear WHERE (category = 'Accessories' AND gear_name LIKE '%Socks%') AND gear_quantity > 0;

-- Mark jersey items as having sizes using a safe approach
-- Create a temporary procedure to handle the updates safely
DELIMITER //
CREATE PROCEDURE UpdateGearSizes()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE gear_id_var INT;
    
    -- Cursor for Jersey items
    DECLARE jersey_cursor CURSOR FOR 
        SELECT gear_id FROM gear WHERE category = 'Jersey';
    
    -- Cursor for Socks items  
    DECLARE socks_cursor CURSOR FOR 
        SELECT gear_id FROM gear WHERE category = 'Accessories' AND gear_name LIKE '%Socks%';
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- Update Jersey items
    OPEN jersey_cursor;
    jersey_loop: LOOP
        FETCH jersey_cursor INTO gear_id_var;
        IF done THEN
            LEAVE jersey_loop;
        END IF;
        UPDATE gear SET has_sizes = 1 WHERE gear_id = gear_id_var;
    END LOOP;
    CLOSE jersey_cursor;
    
    -- Reset done flag
    SET done = FALSE;
    
    -- Update Socks items
    OPEN socks_cursor;
    socks_loop: LOOP
        FETCH socks_cursor INTO gear_id_var;
        IF done THEN
            LEAVE socks_loop;
        END IF;
        UPDATE gear SET has_sizes = 1 WHERE gear_id = gear_id_var;
    END LOOP;
    CLOSE socks_cursor;
    
END//
DELIMITER ;

-- Execute the procedure
CALL UpdateGearSizes();

-- Drop the temporary procedure
DROP PROCEDURE UpdateGearSizes;

-- Add admin_notes field to reservations table for admin comments
ALTER TABLE `mydb`.`reservations` 
ADD COLUMN `admin_notes` TEXT NULL COMMENT 'Admin comments for reservation management' AFTER `notes`;

-- Just add hospitality packages to existing events table
INSERT INTO events (event_name, event_desc, event_date, event_time, location, event_type, ticket_price, capacity, available_tickets, status) VALUES 
('Executive Box Experience', 'Premium hospitality experience with private box seating, gourmet dining, and exclusive amenities', '2025-08-15', '15:00:00', 'Executive Box A1', 'Hospitality', 250.00, 8, 8, 'upcoming'),
('Legends Lounge', 'Meet Rangers legends while enjoying premium dining and exclusive lounge access', '2025-08-15', '14:30:00', 'Legends Lounge', 'Hospitality', 150.00, 20, 20, 'upcoming'),
('Pitch Side VIP', 'Ultimate matchday experience with pitch access and premium amenities', '2025-08-15', '14:00:00', 'Pitch Side Section', 'Hospitality', 400.00, 4, 4, 'upcoming');

-- 1. ALTER existing refund_requests table to add missing columns for payment processors
-- Add refund_type column (only gear and hospitality packages)
ALTER TABLE refund_requests 
ADD COLUMN IF NOT EXISTS refund_type ENUM('gear', 'hospitality') NULL AFTER payment_id;

-- Add payment processor tracking columns
ALTER TABLE refund_requests 
ADD COLUMN IF NOT EXISTS processor_refund_id VARCHAR(255) NULL AFTER refund_id;

ALTER TABLE refund_requests 
ADD COLUMN IF NOT EXISTS payment_processor ENUM('stripe', 'paypal', 'manual') NULL AFTER processor_refund_id;

-- 2. Modify existing columns to support new requirements
-- Make order_id nullable since hospitality refunds might use reservation_id instead
ALTER TABLE refund_requests 
MODIFY COLUMN order_id INT NULL;

-- 3. Add indexes for new columns (only if they don't exist)
-- Check if indexes exist first to avoid duplicate errors
SET @sql = CONCAT('CREATE INDEX IF NOT EXISTS idx_refund_type ON refund_requests(refund_type)');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = CONCAT('CREATE INDEX IF NOT EXISTS idx_processor_refund_id ON refund_requests(processor_refund_id)');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = CONCAT('CREATE INDEX IF NOT EXISTS idx_payment_processor ON refund_requests(payment_processor)');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 4. Update existing records to have proper refund_type and payment_processor
-- Set default refund_type for existing records (assume they are gear refunds)
UPDATE refund_requests 
SET refund_type = 'gear' 
WHERE refund_type IS NULL;

-- Set default payment_processor for existing records
UPDATE refund_requests 
SET payment_processor = 'manual' 
WHERE payment_processor IS NULL;

-- 5. Update payments table to support hospitality type if not already there
ALTER TABLE payments 
MODIFY COLUMN payment_type ENUM('gear', 'ticket', 'membership', 'unified', 'hospitality') DEFAULT 'unified';

-- 7. Show final structure
DESCRIBE refund_requests;

ALTER TABLE order_items ADD COLUMN size VARCHAR(10) NULL;

ALTER TABLE refund_requests ADD COLUMN refund_items JSON NULL;

ALTER TABLE payments ADD COLUMN payment_method VARCHAR(50) AFTER payment_method_id;

ALTER TABLE reservations ADD COLUMN reservation_type VARCHAR(50) AFTER status;